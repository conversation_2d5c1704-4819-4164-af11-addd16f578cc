/**
 * 后台管理框架 - AdminFramework
 * 提供单页面应用的页面管理、路由、事件处理等功能
 *
 * 设计原则：
 * 1. 兼容现有页面的JavaScript逻辑
 * 2. 提供可靠的页面加载和事件绑定机制
 * 3. 支持动态页面加载和卸载
 * 4. 确保事件监听器的正确绑定和清理
 */

(function(window) {
    'use strict';

    // 脚本沙箱管理器
    const ScriptSandboxManager = {
        // 存储每个页面的沙箱环境
        sandboxes: new Map(),

        // 存储每个页面的事件监听器
        eventListeners: new Map(),

        // 跟踪已加载的脚本文件
        loadedScripts: new Set(),

        // 跟踪正在加载的脚本文件
        loadingScripts: new Map(),

        /**
         * 为指定路由创建沙箱环境
         */
        createSandbox: function(route) {
            const sandboxName = `PageSandbox_${route}`;

            // 创建沙箱命名空间
            const sandbox = {
                // 沙箱内的全局变量
                globals: {},

                // 沙箱内的函数
                functions: {},

                // 事件监听器列表
                listeners: [],

                // 定时器列表
                timers: [],

                // 沙箱API - 提供安全的框架访问接口
                api: {
                    // 安全的DOM查询（限制在当前页面内容区域）
                    querySelector: function(selector) {
                        const pageContent = document.getElementById('afPageContent');
                        return pageContent ? pageContent.querySelector(selector) : null;
                    },

                    querySelectorAll: function(selector) {
                        const pageContent = document.getElementById('afPageContent');
                        return pageContent ? pageContent.querySelectorAll(selector) : [];
                    },

                    getElementById: function(id) {
                        const pageContent = document.getElementById('afPageContent');
                        return pageContent ? pageContent.querySelector(`#${id}`) : null;
                    },

                    // 安全的事件绑定
                    addEventListener: function(element, event, handler, options) {
                        if (element && typeof handler === 'function') {
                            element.addEventListener(event, handler, options);
                            // 记录事件监听器以便清理
                            sandbox.listeners.push({
                                element: element,
                                event: event,
                                handler: handler,
                                options: options
                            });
                        }
                    },

                    // 安全的定时器
                    setTimeout: function(callback, delay) {
                        const timerId = setTimeout(callback, delay);
                        sandbox.timers.push(timerId);
                        return timerId;
                    },

                    setInterval: function(callback, delay) {
                        const timerId = setInterval(callback, delay);
                        sandbox.timers.push(timerId);
                        return timerId;
                    },

                    // 框架导航功能
                    navigateTo: function(route) {
                        if (window.AdminFramework && typeof window.AdminFramework.navigateTo === 'function') {
                            window.AdminFramework.navigateTo(route);
                        }
                    },

                    // 显示通知
                    showNotification: function(message, type = 'info') {
                        console.log(`[${route}] ${type.toUpperCase()}: ${message}`);
                        // 这里可以集成实际的通知系统
                    },

                    // 获取当前路由
                    getCurrentRoute: function() {
                        return route;
                    },

                    // 安全的AJAX请求
                    fetch: function(url, options = {}) {
                        // 确保请求在合理范围内
                        if (typeof url !== 'string') {
                            throw new Error('URL必须是字符串');
                        }

                        // 添加默认headers
                        const defaultOptions = {
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'Content-Type': 'application/json',
                                ...options.headers
                            },
                            ...options
                        };

                        return fetch(url, defaultOptions);
                    },

                    // 安全的本地存储访问
                    localStorage: {
                        getItem: function(key) {
                            return localStorage.getItem(`${route}_${key}`);
                        },
                        setItem: function(key, value) {
                            localStorage.setItem(`${route}_${key}`, value);
                        },
                        removeItem: function(key) {
                            localStorage.removeItem(`${route}_${key}`);
                        }
                    },

                    // 提供常用的工具函数
                    utils: {
                        // 防抖函数
                        debounce: function(func, wait) {
                            let timeout;
                            return function executedFunction(...args) {
                                const later = () => {
                                    clearTimeout(timeout);
                                    func(...args);
                                };
                                clearTimeout(timeout);
                                timeout = setTimeout(later, wait);
                                sandbox.timers.push(timeout);
                            };
                        },

                        // 节流函数
                        throttle: function(func, limit) {
                            let inThrottle;
                            return function(...args) {
                                if (!inThrottle) {
                                    func.apply(this, args);
                                    inThrottle = true;
                                    const timeoutId = setTimeout(() => inThrottle = false, limit);
                                    sandbox.timers.push(timeoutId);
                                }
                            };
                        },

                        // 格式化日期
                        formatDate: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
                            if (!(date instanceof Date)) {
                                date = new Date(date);
                            }

                            const year = date.getFullYear();
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const day = String(date.getDate()).padStart(2, '0');
                            const hours = String(date.getHours()).padStart(2, '0');
                            const minutes = String(date.getMinutes()).padStart(2, '0');
                            const seconds = String(date.getSeconds()).padStart(2, '0');

                            return format
                                .replace('YYYY', year)
                                .replace('MM', month)
                                .replace('DD', day)
                                .replace('HH', hours)
                                .replace('mm', minutes)
                                .replace('ss', seconds);
                        }
                    }
                }
            };

            // 存储沙箱
            this.sandboxes.set(route, sandbox);

            // 将沙箱暴露到全局（用于调试）
            if (window.AdminFrameworkConfig && window.AdminFrameworkConfig.debug) {
                window[sandboxName] = sandbox;
            }

            console.log(`ScriptSandbox: 为路由 ${route} 创建沙箱环境`);
            return sandbox;
        },

        /**
         * 在沙箱中执行脚本
         */
        executeScript: function(route, scriptContent, context = {}) {
            let sandbox = this.sandboxes.get(route);
            if (!sandbox) {
                console.log(`ScriptSandbox: 为路由 ${route} 创建新的沙箱环境`);
                sandbox = this.createSandbox(route);
            } else {
                console.log(`ScriptSandbox: 重用路由 ${route} 的现有沙箱环境`);
            }

            try {
                // 创建执行上下文
                const executionContext = {
                    // 提供沙箱API
                    ...sandbox.api,

                    // 提供常用的全局对象（受限版本）
                    console: console,
                    document: {
                        getElementById: sandbox.api.getElementById,
                        querySelector: sandbox.api.querySelector,
                        querySelectorAll: sandbox.api.querySelectorAll,
                        addEventListener: function(event, handler, options) {
                            document.addEventListener(event, handler, options);
                            sandbox.listeners.push({
                                element: document,
                                event: event,
                                handler: handler,
                                options: options
                            });
                        }
                    },

                    // 提供受限的window对象
                    window: {
                        setTimeout: sandbox.api.setTimeout,
                        setInterval: sandbox.api.setInterval,
                        location: window.location,
                        alert: window.alert,
                        confirm: window.confirm
                    },

                    // 传入的上下文变量
                    ...context,

                    // 沙箱全局变量
                    ...sandbox.globals
                };

                // 使用Function构造器在受控环境中执行脚本
                const scriptFunction = new Function(
                    ...Object.keys(executionContext),
                    scriptContent
                );

                // 执行脚本
                const result = scriptFunction.apply(null, Object.values(executionContext));

                console.log(`ScriptSandbox: 在路由 ${route} 中成功执行脚本`);
                console.log(`ScriptSandbox: 脚本内容预览: ${scriptContent.substring(0, 100)}...`);
                return result;

            } catch (error) {
                console.error(`ScriptSandbox: 在路由 ${route} 中执行脚本失败:`, error);
                console.error('脚本内容:', scriptContent);
                return null;
            }
        },

        /**
         * 提取HTML中的脚本内容
         */
        extractScripts: function(html) {
            const scripts = [];
            const inlineEvents = [];

            // 创建临时DOM来解析HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            // 提取script标签
            const scriptTags = tempDiv.querySelectorAll('script');
            scriptTags.forEach(script => {
                if (script.src) {
                    // 外部脚本 - 现在也要处理
                    scripts.push({
                        type: 'external',
                        src: script.src,
                        content: null
                    });
                } else if (script.textContent.trim()) {
                    // 内联脚本
                    scripts.push({
                        type: 'inline',
                        src: null,
                        content: script.textContent
                    });
                }
            });

            // 提取内联事件处理器
            const elementsWithEvents = tempDiv.querySelectorAll('*');
            elementsWithEvents.forEach(element => {
                // 检查常见的事件属性
                const eventAttributes = ['onclick', 'onchange', 'onsubmit', 'onload', 'onmouseover', 'onmouseout'];
                eventAttributes.forEach(attr => {
                    if (element.hasAttribute(attr)) {
                        const handler = element.getAttribute(attr);
                        inlineEvents.push({
                            element: element,
                            event: attr.substring(2), // 移除'on'前缀
                            handler: handler,
                            selector: this.generateSelector(element)
                        });
                    }
                });
            });

            return { scripts, inlineEvents };
        },

        /**
         * 动态加载外部脚本
         */
        loadExternalScript: function(route, scriptSrc) {
            return new Promise((resolve, reject) => {
                // 检查脚本是否已经加载
                if (this.loadedScripts.has(scriptSrc)) {
                    console.log(`ScriptSandbox: 脚本 ${scriptSrc} 已加载，跳过重复加载`);
                    resolve();
                    return;
                }

                // 检查脚本是否正在加载
                if (this.loadingScripts.has(scriptSrc)) {
                    console.log(`ScriptSandbox: 脚本 ${scriptSrc} 正在加载，等待完成`);
                    // 返回正在加载的Promise
                    this.loadingScripts.get(scriptSrc).then(resolve).catch(reject);
                    return;
                }

                // 检查DOM中是否已存在该脚本
                const existingScript = document.querySelector(`script[src="${scriptSrc}"]`);
                if (existingScript) {
                    console.log(`ScriptSandbox: 脚本 ${scriptSrc} 在DOM中已存在，标记为已加载`);
                    this.loadedScripts.add(scriptSrc);
                    resolve();
                    return;
                }

                // 创建加载Promise
                const loadingPromise = new Promise((loadResolve, loadReject) => {
                    // 创建script标签
                    const script = document.createElement('script');
                    script.src = scriptSrc;
                    script.type = 'text/javascript';

                    script.onload = () => {
                        console.log(`ScriptSandbox: 外部脚本加载成功: ${scriptSrc}`);
                        this.loadedScripts.add(scriptSrc);
                        this.loadingScripts.delete(scriptSrc);
                        loadResolve();
                    };

                    script.onerror = (error) => {
                        console.error(`ScriptSandbox: 外部脚本加载失败: ${scriptSrc}`, error);
                        this.loadingScripts.delete(scriptSrc);
                        loadReject(error);
                    };

                    // 添加到head中
                    document.head.appendChild(script);
                });

                // 记录正在加载的脚本
                this.loadingScripts.set(scriptSrc, loadingPromise);

                // 返回加载Promise
                loadingPromise.then(resolve).catch(reject);
            });
        },

        /**
         * 根据路由推断对应的脚本文件
         */
        getScriptPathForRoute: function(route) {
            // 页面脚本文件映射规则
            const scriptMappings = {
                'home': '/static/js/pages/home.js',
                'category': '/static/js/pages/category.js',
                'productlist': '/static/js/pages/productlist.js',
                'cardstock': '/static/js/pages/cardstock.js',
                'coupon': '/static/js/pages/coupon.js',
                'PriceTemplate': '/static/js/pages/PriceTemplate.js',
                'DockingCenter': '/static/js/pages/DockingCenter.js',
                'orderlist': '/static/js/pages/orderlist.js',
                'userlist': '/static/js/pages/userlist.js',
                'usergroup': '/static/js/pages/usergroup.js',
                'payment': '/static/js/pages/payment.js',
                'plugin': '/static/js/pages/plugin.js'
            };

            return scriptMappings[route] || null;
        },

        /**
         * 获取页面初始化函数映射
         */
        getInitializationFunctionForRoute: function(route) {
            // 页面初始化函数映射规则
            const initFunctionMappings = {
                'home': 'initializeHomePage',
                'category': 'initializeCategoryPageForFramework',
                'productlist': 'initializeProductListPage',
                'cardstock': 'initializeCardStockPage',
                'coupon': 'initializeCouponPage',
                'PriceTemplate': 'initializePriceTemplateForFramework',
                'DockingCenter': 'initializeDockingCenterPage',
                'orderlist': 'initializeOrderListPage',
                'userlist': 'initializeUserListPage',
                'usergroup': 'initializeUserGroupPage',
                'payment': 'initializePaymentPage',
                'plugin': 'initializePluginPage'
            };

            return initFunctionMappings[route] || null;
        },

        /**
         * 强制重新初始化页面
         */
        forceReinitializePage: function(route) {
            console.log(`ScriptSandbox: 强制重新初始化页面 (${route})`);

            // 获取页面的初始化函数名
            const initFunctionName = this.getInitializationFunctionForRoute(route);

            if (initFunctionName && typeof window[initFunctionName] === 'function') {
                console.log(`ScriptSandbox: 调用页面初始化函数 ${initFunctionName}`);
                try {
                    window[initFunctionName]();
                    return true;
                } catch (error) {
                    console.error(`ScriptSandbox: 调用初始化函数 ${initFunctionName} 失败:`, error);
                    return false;
                }
            } else {
                console.log(`ScriptSandbox: 页面 ${route} 没有标准初始化函数 ${initFunctionName || '(未定义)'}`);

                // 尝试一些通用的初始化函数名
                const genericFunctionNames = [
                    'initializePage',
                    'init',
                    `initialize${route.charAt(0).toUpperCase() + route.slice(1)}`,
                    'pageInit'
                ];

                for (const funcName of genericFunctionNames) {
                    if (typeof window[funcName] === 'function') {
                        console.log(`ScriptSandbox: 找到通用初始化函数 ${funcName}，尝试调用`);
                        try {
                            window[funcName]();
                            return true;
                        } catch (error) {
                            console.warn(`ScriptSandbox: 调用通用初始化函数 ${funcName} 失败:`, error);
                        }
                    }
                }

                // 如果没有找到初始化函数，返回特殊值表示需要重新加载脚本
                console.log(`ScriptSandbox: 没有找到初始化函数，需要重新加载脚本 (${route})`);
                return 'NEED_RELOAD'; // 返回特殊值，让调用方处理重新加载
            }
        },

        /**
         * 查找所有匹配的脚本标签
         */
        findMatchingScripts: function(scriptPath) {
            console.log(`ScriptSandbox: 查找匹配的脚本标签，目标路径: ${scriptPath}`);

            const allScripts = document.querySelectorAll('script[src]');
            const matchingScripts = [];

            console.log(`ScriptSandbox: 页面中共有 ${allScripts.length} 个脚本标签`);

            // 生成可能的路径变体
            const pathVariants = [
                scriptPath, // 原始路径
                window.location.origin + scriptPath, // 绝对路径
                scriptPath.replace(/^\//, ''), // 移除开头的斜杠
                '/' + scriptPath.replace(/^\//, '') // 确保开头有斜杠
            ];

            console.log(`ScriptSandbox: 路径变体:`, pathVariants);

            allScripts.forEach((script, index) => {
                const src = script.src;
                console.log(`ScriptSandbox: 检查脚本 ${index + 1}: ${src}`);

                // 检查是否匹配任何路径变体
                for (const variant of pathVariants) {
                    if (src === variant || src.endsWith(variant) || variant.endsWith(src.replace(window.location.origin, ''))) {
                        console.log(`ScriptSandbox: 找到匹配脚本 (变体匹配): ${src}`);
                        matchingScripts.push(script);
                        break;
                    }
                }

                // 额外检查：如果路径包含相同的文件名
                const scriptFileName = scriptPath.split('/').pop();
                if (scriptFileName && src.includes(scriptFileName)) {
                    // 进一步验证是否是同一个文件
                    const srcFileName = src.split('/').pop().split('?')[0]; // 移除查询参数
                    if (srcFileName === scriptFileName) {
                        console.log(`ScriptSandbox: 找到匹配脚本 (文件名匹配): ${src}`);
                        matchingScripts.push(script);
                    }
                }
            });

            // 去重
            const uniqueScripts = [...new Set(matchingScripts)];
            console.log(`ScriptSandbox: 最终找到 ${uniqueScripts.length} 个匹配的脚本`);

            return uniqueScripts;
        },

        /**
         * 重新加载页面脚本
         */
        reloadPageScript: function(route) {
            console.log(`ScriptSandbox: 重新加载页面脚本 (${route})`);

            const scriptPath = this.getScriptPathForRoute(route);
            if (!scriptPath) {
                console.log(`ScriptSandbox: 路由 ${route} 没有对应的脚本文件`);
                return Promise.resolve(false);
            }

            return new Promise((resolve) => {
                // 查找所有匹配的脚本标签
                const existingScripts = this.findMatchingScripts(scriptPath);
                console.log(`ScriptSandbox: 找到 ${existingScripts.length} 个匹配的脚本标签`);

                existingScripts.forEach(script => {
                    console.log(`ScriptSandbox: 移除现有脚本标签: ${script.src}`);
                    script.remove();
                });

                // 从加载状态中移除（检查多种路径格式）
                const pathsToRemove = [
                    scriptPath,
                    window.location.origin + scriptPath
                ];

                pathsToRemove.forEach(path => {
                    this.loadedScripts.delete(path);
                    this.loadingScripts.delete(path);
                });

                // 添加时间戳防止缓存
                const timestamp = new Date().getTime();
                const scriptPathWithTimestamp = `${scriptPath}?t=${timestamp}`;

                console.log(`ScriptSandbox: 开始重新加载脚本: ${scriptPathWithTimestamp}`);

                // 创建新的script标签
                const script = document.createElement('script');
                script.src = scriptPathWithTimestamp;
                script.type = 'text/javascript';

                script.onload = () => {
                    console.log(`ScriptSandbox: 脚本重新加载成功: ${scriptPath}`);

                    // 标记为已加载（使用原始路径，不包含时间戳）
                    this.loadedScripts.add(scriptPath);

                    // 延迟一点时间，确保脚本完全执行
                    setTimeout(() => {
                        resolve(true);
                    }, 100);
                };

                script.onerror = (error) => {
                    console.error(`ScriptSandbox: 脚本重新加载失败: ${scriptPath}`, error);
                    resolve(false);
                };

                // 添加到head中
                document.head.appendChild(script);
            });
        },

        /**
         * 强制重新执行页面脚本（用于没有初始化函数的页面）
         */
        forceReexecutePageScript: function(route) {
            console.log(`ScriptSandbox: 强制重新执行页面脚本 (${route})`);

            return this.reloadPageScript(route).then(success => {
                if (success) {
                    console.log(`ScriptSandbox: 页面 ${route} 脚本重新执行成功`);

                    // 再次尝试查找初始化函数
                    const initFunctionName = this.getInitializationFunctionForRoute(route);
                    if (initFunctionName && typeof window[initFunctionName] === 'function') {
                        console.log(`ScriptSandbox: 重新加载后找到初始化函数 ${initFunctionName}，尝试调用`);
                        try {
                            window[initFunctionName]();
                            return true;
                        } catch (error) {
                            console.error(`ScriptSandbox: 重新加载后调用初始化函数失败:`, error);
                        }
                    }

                    return true; // 脚本重新执行成功，即使没有找到初始化函数
                } else {
                    console.error(`ScriptSandbox: 页面 ${route} 脚本重新执行失败`);
                    return false;
                }
            });
        },

        /**
         * 加载页面对应的脚本文件
         */
        loadPageScript: function(route) {
            const scriptPath = this.getScriptPathForRoute(route);
            if (!scriptPath) {
                console.log(`ScriptSandbox: 路由 ${route} 没有对应的脚本文件`);
                return Promise.resolve();
            }

            console.log(`ScriptSandbox: 开始加载页面脚本: ${route} -> ${scriptPath}`);
            return this.loadExternalScript(route, scriptPath);
        },

        /**
         * 为元素生成CSS选择器
         */
        generateSelector: function(element) {
            if (element.id) {
                return `#${element.id}`;
            }

            if (element.className) {
                const classes = element.className.split(' ').filter(c => c.trim());
                if (classes.length > 0) {
                    return `.${classes.join('.')}`;
                }
            }

            // 使用标签名和位置
            const tagName = element.tagName.toLowerCase();
            const parent = element.parentElement;
            if (parent) {
                const siblings = Array.from(parent.children).filter(child => child.tagName === element.tagName);
                const index = siblings.indexOf(element);
                return `${tagName}:nth-of-type(${index + 1})`;
            }

            return tagName;
        },

        /**
         * 清理指定路由的沙箱环境
         */
        cleanup: function(route) {
            const sandbox = this.sandboxes.get(route);
            if (!sandbox) {
                console.log(`ScriptSandbox: 路由 ${route} 没有沙箱环境需要清理`);
                return;
            }

            console.log(`ScriptSandbox: 清理路由 ${route} 的沙箱环境`);
            console.log(`ScriptSandbox: 清理 ${sandbox.listeners.length} 个事件监听器和 ${sandbox.timers.length} 个定时器`);

            // 清理事件监听器
            sandbox.listeners.forEach(listener => {
                try {
                    listener.element.removeEventListener(listener.event, listener.handler, listener.options);
                } catch (error) {
                    console.warn('清理事件监听器失败:', error);
                }
            });

            // 清理定时器
            sandbox.timers.forEach(timerId => {
                try {
                    clearTimeout(timerId);
                    clearInterval(timerId);
                } catch (error) {
                    console.warn('清理定时器失败:', error);
                }
            });

            // 清理全局变量
            const sandboxName = `PageSandbox_${route}`;
            if (window[sandboxName]) {
                delete window[sandboxName];
            }

            // 移除沙箱
            this.sandboxes.delete(route);
        },

        /**
         * 清理所有沙箱环境
         */
        cleanupAll: function() {
            console.log('ScriptSandbox: 清理所有沙箱环境');
            for (const route of this.sandboxes.keys()) {
                this.cleanup(route);
            }

            // 清理脚本加载状态（可选，通常保留以避免重复加载）
            // this.loadedScripts.clear();
            // this.loadingScripts.clear();
        },

        /**
         * 重置脚本加载状态（用于调试或特殊情况）
         */
        resetScriptLoadingState: function() {
            console.log('ScriptSandbox: 重置脚本加载状态');
            this.loadedScripts.clear();
            this.loadingScripts.clear();
        }
    };

    // AdminFramework 主对象
    const AdminFramework = {
        // 当前加载的页面信息
        currentPage: null,
        currentRoute: null,

        // 页面缓存
        pageCache: new Map(),

        // 页面脚本缓存
        scriptCache: new Map(),

        // 已加载的页面脚本
        loadedScripts: new Set(),

        // 页面清理函数缓存
        cleanupFunctions: new Map(),

        /**
         * 初始化框架
         */
        init: function() {
            console.log('AdminFramework 初始化开始');

            // 隐藏加载遮罩
            this.hideLoadingOverlay();

            // 绑定导航事件
            this.bindNavigationEvents();

            // 初始化路由
            this.initializeRouting();

            // 设置全局错误处理
            this.setupErrorHandling();

            console.log('AdminFramework 初始化完成');
        },

        /**
         * 隐藏加载遮罩
         */
        hideLoadingOverlay: function() {
            setTimeout(() => {
                const loadingOverlay = document.getElementById('afLoadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.classList.add('af-hidden');
                    setTimeout(() => {
                        loadingOverlay.style.display = 'none';
                    }, 300);
                }
            }, 500);
        },

        /**
         * 绑定导航事件
         */
        bindNavigationEvents: function() {
            const navList = document.querySelector('.af-nav-list');
            if (!navList) return;

            navList.addEventListener('click', (e) => {
                const navItem = e.target.closest('.af-nav-item');
                if (!navItem) return;

                e.preventDefault();

                const route = navItem.getAttribute('data-af-route');
                if (route) {
                    this.navigateTo(route);
                }
            });
        },

        /**
         * 初始化路由
         */
        initializeRouting: function() {
            // 检查URL哈希值
            const hash = window.location.hash.replace('#', '');
            const defaultRoute = hash || 'home';

            // 添加popstate事件监听
            window.addEventListener('popstate', (event) => {
                if (event.state?.route) {
                    this.loadPage(event.state.route);
                } else {
                    this.loadPage(defaultRoute);
                }
            });

            // 加载默认页面
            this.navigateTo(defaultRoute);
        },

        /**
         * 导航到指定路由
         */
        navigateTo: function(route) {
            // 移除早期返回，允许重新访问当前页面以重新执行脚本
            const isRevisit = (route === this.currentRoute);

            if (isRevisit) {
                console.log(`重新访问当前路由: ${route}`);
            } else {
                console.log(`导航到新路由: ${route}`);
                // 只有在访问新路由时才更新URL
                window.history.pushState({ route }, '', `#${route}`);
            }

            // 加载页面（无论是新访问还是重新访问）
            this.loadPage(route);

            // 更新导航状态
            this.updateNavigationState(route);
        },

        /**
         * 加载页面
         */
        loadPage: function(route) {
            console.log(`开始加载页面: ${route}`);

            // 显示进度条
            this.showProgressBar();

            // 清理当前页面（传入当前路由以确保正确清理）
            this.cleanupCurrentPage();

            // 如果是重新访问同一页面，强制清理该页面的沙箱环境
            if (route === this.currentRoute) {
                console.log(`强制清理重新访问页面的沙箱环境: ${route}`);
                ScriptSandboxManager.cleanup(route);
            }

            // 构建正确的请求URL
            const config = window.AdminFrameworkConfig || {};
            const basePath = config.frameworkPagePath || '/admin/backend/framework/page';
            const requestUrl = `${basePath}/${route}`;

            console.log(`[AdminFramework] 加载页面: ${route}, URL: ${requestUrl}`);

            // 发送AJAX请求加载页面内容
            fetch(requestUrl, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(html => {
                // 更新页面内容
                this.updatePageContent(html, route);

                // 初始化页面脚本
                this.initializePageScripts(route);

                // 更新当前页面信息
                this.currentPage = html;
                this.currentRoute = route;

                console.log(`页面加载完成: ${route}`);
            })
            .catch(error => {
                console.error(`页面加载失败: ${route}`, error);
                this.showErrorPage(route, error);
            })
            .finally(() => {
                // 隐藏进度条
                this.hideProgressBar();
            });
        },

        /**
         * 更新页面内容
         */
        updatePageContent: function(html, route) {
            const pageContent = document.getElementById('afPageContent');
            if (pageContent) {
                // 过滤掉可能引用index.js的script标签
                let filteredHtml = html;

                // 移除对index.js的引用
                filteredHtml = filteredHtml.replace(/<script[^>]*src[^>]*index\.js[^>]*><\/script>/gi, '');
                filteredHtml = filteredHtml.replace(/<script[^>]*src[^>]*\/static\/js\/index\.js[^>]*><\/script>/gi, '');

                console.log(`AdminFramework: 更新页面内容 (${route})`);

                // 提取脚本内容
                const { scripts, inlineEvents } = ScriptSandboxManager.extractScripts(filteredHtml);

                // 移除原始HTML中的script标签，避免重复执行
                let cleanHtml = filteredHtml;
                cleanHtml = cleanHtml.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

                // 更新页面内容（不包含script标签）
                pageContent.innerHTML = cleanHtml;

                // 处理脚本加载和执行
                this.processPageScripts(route, scripts, inlineEvents);

                // 缓存页面内容
                this.pageCache.set(route, filteredHtml);
            }
        },

        /**
         * 处理页面脚本加载和执行
         */
        processPageScripts: function(route, scripts, inlineEvents) {
            console.log(`AdminFramework: 开始处理页面脚本 (${route})`);

            // 检查是否为重新访问，如果是，强制重新执行内联脚本
            const isRevisit = (route === this.currentRoute);
            if (isRevisit) {
                console.log(`AdminFramework: 检测到重新访问页面 (${route})，将强制重新执行内联脚本`);
            }

            // 首先加载页面对应的主脚本文件（如果存在）
            ScriptSandboxManager.loadPageScript(route)
                .then(() => {
                    console.log(`AdminFramework: 页面主脚本加载完成 (${route})`);

                    // 然后处理HTML中的外部脚本
                    const externalScripts = scripts.filter(s => s.type === 'external');
                    const loadPromises = externalScripts.map(script => {
                        console.log(`AdminFramework: 加载外部脚本 ${script.src}`);
                        return ScriptSandboxManager.loadExternalScript(route, script.src);
                    });

                    return Promise.all(loadPromises);
                })
                .then(() => {
                    console.log(`AdminFramework: 所有外部脚本加载完成 (${route})`);

                    // 等待一段时间确保脚本完全执行
                    return new Promise(resolve => setTimeout(resolve, 100));
                })
                .then(() => {
                    // 执行内联脚本
                    const inlineScripts = scripts.filter(s => s.type === 'inline');
                    let hasInlineScripts = false;

                    inlineScripts.forEach(script => {
                        if (script.content) {
                            console.log(`AdminFramework: 在沙箱中执行内联脚本 (${route})`);
                            ScriptSandboxManager.executeScript(route, script.content);
                            hasInlineScripts = true;
                        }
                    });

                    // 重新绑定内联事件处理器
                    this.rebindInlineEvents(route, inlineEvents);

                    // 如果没有内联脚本但有内联事件，或者是重新访问，强制重新初始化
                    if (!hasInlineScripts || isRevisit) {
                        console.log(`AdminFramework: 页面 ${route} 没有内联脚本或为重新访问，尝试强制重新初始化`);

                        // 延迟执行，确保外部脚本完全加载和DOM元素可用
                        setTimeout(() => {
                            const initResult = ScriptSandboxManager.forceReinitializePage(route);
                            if (initResult === true) {
                                console.log(`AdminFramework: 页面 ${route} 重新初始化成功`);
                            } else if (initResult === 'NEED_RELOAD') {
                                console.log(`AdminFramework: 页面 ${route} 需要重新加载脚本`);

                                // 重新加载脚本
                                ScriptSandboxManager.forceReexecutePageScript(route).then(success => {
                                    if (success) {
                                        console.log(`AdminFramework: 页面 ${route} 脚本重新执行成功`);
                                    } else {
                                        console.log(`AdminFramework: 页面 ${route} 脚本重新执行失败`);
                                    }
                                });
                            } else {
                                console.log(`AdminFramework: 页面 ${route} 重新初始化失败`);
                            }
                        }, 300); // 增加延迟，确保DOM完全准备好
                    }

                    console.log(`AdminFramework: 页面脚本处理完成 (${route})`);
                })
                .catch(error => {
                    console.error(`AdminFramework: 页面脚本处理失败 (${route}):`, error);

                    // 即使外部脚本加载失败，也要尝试执行内联脚本
                    const inlineScripts = scripts.filter(s => s.type === 'inline');
                    let hasInlineScripts = false;

                    inlineScripts.forEach(script => {
                        if (script.content) {
                            console.log(`AdminFramework: 备用执行内联脚本 (${route})`);
                            ScriptSandboxManager.executeScript(route, script.content);
                            hasInlineScripts = true;
                        }
                    });

                    // 重新绑定内联事件处理器
                    this.rebindInlineEvents(route, inlineEvents);

                    // 即使脚本加载失败，也尝试重新初始化（可能脚本已经存在）
                    if (!hasInlineScripts || isRevisit) {
                        console.log(`AdminFramework: 脚本加载失败，但仍尝试重新初始化 (${route})`);

                        setTimeout(() => {
                            const initResult = ScriptSandboxManager.forceReinitializePage(route);
                            if (initResult === true) {
                                console.log(`AdminFramework: 页面 ${route} 备用重新初始化成功`);
                            } else if (initResult === 'NEED_RELOAD') {
                                console.log(`AdminFramework: 页面 ${route} 备用需要重新加载脚本`);

                                // 备用方案：重新执行脚本
                                ScriptSandboxManager.forceReexecutePageScript(route).then(success => {
                                    if (success) {
                                        console.log(`AdminFramework: 页面 ${route} 备用脚本重新执行成功`);
                                    } else {
                                        console.log(`AdminFramework: 页面 ${route} 备用脚本重新执行失败`);
                                    }
                                });
                            } else {
                                console.log(`AdminFramework: 页面 ${route} 备用重新初始化失败`);
                            }
                        }, 300);
                    }
                });
        },

        /**
         * 重新绑定内联事件处理器
         */
        rebindInlineEvents: function(route, inlineEvents) {
            console.log(`AdminFramework: 重新绑定内联事件处理器 (${route}), 共 ${inlineEvents.length} 个事件`);

            const pageContent = document.getElementById('afPageContent');
            if (!pageContent) return;

            inlineEvents.forEach(eventInfo => {
                try {
                    // 在当前页面内容中查找对应元素
                    let targetElement = null;

                    // 优先使用ID查找
                    if (eventInfo.selector.startsWith('#')) {
                        targetElement = pageContent.querySelector(eventInfo.selector);
                    } else {
                        // 使用其他选择器
                        targetElement = pageContent.querySelector(eventInfo.selector);
                    }

                    if (targetElement) {
                        // 创建事件处理函数
                        const handlerFunction = this.createSandboxEventHandler(route, eventInfo.handler);

                        // 绑定事件
                        targetElement.addEventListener(eventInfo.event, handlerFunction);

                        // 记录到沙箱管理器中以便清理
                        const sandbox = ScriptSandboxManager.sandboxes.get(route);
                        if (sandbox) {
                            sandbox.listeners.push({
                                element: targetElement,
                                event: eventInfo.event,
                                handler: handlerFunction,
                                options: undefined
                            });
                        }

                        console.log(`AdminFramework: 成功绑定事件 ${eventInfo.event} 到元素 ${eventInfo.selector}`);
                    } else {
                        console.warn(`AdminFramework: 未找到元素 ${eventInfo.selector}，无法绑定事件 ${eventInfo.event}`);
                    }
                } catch (error) {
                    console.error(`AdminFramework: 绑定事件失败:`, error);
                    console.error('事件信息:', eventInfo);
                }
            });
        },

        /**
         * 创建沙箱化的事件处理函数
         */
        createSandboxEventHandler: function(route, handlerCode) {
            return function(event) {
                try {
                    // 在沙箱中执行事件处理代码
                    const context = {
                        event: event,
                        this: this // 保持this指向
                    };

                    ScriptSandboxManager.executeScript(route, handlerCode, context);
                } catch (error) {
                    console.error(`AdminFramework: 事件处理器执行失败 (${route}):`, error);
                    console.error('处理器代码:', handlerCode);
                }
            };
        },

        /**
         * 初始化页面脚本（已被沙箱系统取代，保留用于兼容性）
         */
        initializePageScripts: function(route) {
            console.log(`初始化页面脚本: ${route} (通过沙箱系统处理)`);

            // 注意：脚本加载和执行现在由沙箱系统在 processPageScripts 中统一处理
            // 这里只保留一些特殊的初始化逻辑，避免重复执行

            // 等待DOM和脚本加载完成
            setTimeout(() => {
                // 只处理需要特殊初始化的页面
                switch(route) {
                    case 'category':
                        // category页面的特殊初始化已由其脚本文件处理
                        console.log(`AdminFramework: category页面脚本已通过沙箱系统加载`);
                        break;
                    case 'home':
                        // home页面的特殊初始化已由其脚本文件处理
                        console.log(`AdminFramework: home页面脚本已通过沙箱系统加载`);
                        break;
                    case 'productlist':
                        // productlist页面的特殊初始化已由其脚本文件处理
                        console.log(`AdminFramework: productlist页面脚本已通过沙箱系统加载`);
                        break;
                    case 'PriceTemplate':
                        // PriceTemplate页面现在也通过沙箱系统处理，不再需要特殊逻辑
                        console.log(`AdminFramework: PriceTemplate页面脚本已通过沙箱系统加载`);
                        break;
                    default:
                        console.log(`AdminFramework: ${route}页面脚本已通过沙箱系统加载`);
                        break;
                }
            }, 200); // 增加延迟，确保沙箱系统完成处理
        },

        /**
         * 初始化分类页面
         */
        initializeCategoryPage: function() {
            console.log('初始化分类页面');

            // 确保category.js中的函数可用
            if (typeof window.initializeCategoryPageForFramework === 'function') {
                window.initializeCategoryPageForFramework();
            } else {
                // 如果函数不存在，使用定时器等待
                let attempts = 0;
                const maxAttempts = 50; // 最多等待5秒

                const waitForCategoryScript = () => {
                    attempts++;
                    if (typeof window.initializeCategoryPageForFramework === 'function') {
                        window.initializeCategoryPageForFramework();
                    } else if (attempts < maxAttempts) {
                        setTimeout(waitForCategoryScript, 100);
                    } else {
                        console.warn('分类页面初始化函数未找到，尝试直接初始化');
                        this.fallbackCategoryInitialization();
                    }
                };

                waitForCategoryScript();
            }
        },

        /**
         * 分类页面备用初始化方法
         */
        fallbackCategoryInitialization: function() {
            // 检查必要的DOM元素是否存在
            const categoryContainer = document.getElementById('category-container');
            if (!categoryContainer) {
                console.warn('分类页面容器未找到');
                return;
            }

            // 尝试调用全局的分类初始化函数
            if (typeof window.fetchCategories === 'function') {
                window.fetchCategories();
            }

            if (typeof window.initializeEventListeners === 'function') {
                window.initializeEventListeners();
            }
        },

        /**
         * 初始化首页
         */
        initializeHomePage: function() {
            console.log('初始化首页');
            // 首页初始化逻辑
        },

        /**
         * 初始化商品列表页面
         */
        initializeProductListPage: function() {
            console.log('初始化商品列表页面');
            // 商品列表页面初始化逻辑
        },

        /**
         * 通用页面初始化
         */
        initializeGenericPage: function(route) {
            console.log(`初始化通用页面: ${route}`);

            // 特殊页面处理 - PriceTemplate页面需要动态加载脚本
            if (route === 'PriceTemplate') {
                this.initializePriceTemplatePage();
            }
            // 可以在这里添加其他页面的特殊处理
        },

        /**
         * 初始化PriceTemplate页面 - 动态加载脚本
         */
        initializePriceTemplatePage: function() {
            console.log('AdminFramework: 开始初始化PriceTemplate页面');

            // 检查脚本是否已经加载或正在加载
            if (this.loadedScripts.has('PriceTemplate.js')) {
                console.log('AdminFramework: PriceTemplate.js已加载，直接调用初始化');
                this.callPriceTemplateInitialization();
                return;
            }

            // 检查是否已经存在该脚本标签
            const existingScript = document.querySelector('script[src="/static/js/pages/PriceTemplate.js"]');
            if (existingScript) {
                console.log('AdminFramework: PriceTemplate.js脚本标签已存在，等待加载完成');
                this.loadedScripts.add('PriceTemplate.js');

                // 延迟调用初始化
                setTimeout(() => {
                    this.callPriceTemplateInitialization();
                }, 200);
                return;
            }

            // 标记正在加载，防止重复加载
            this.loadedScripts.add('PriceTemplate.js');

            // 动态加载PriceTemplate.js脚本
            const script = document.createElement('script');
            script.src = '/static/js/pages/PriceTemplate.js';
            script.type = 'text/javascript';

            script.onload = () => {
                console.log('AdminFramework: PriceTemplate.js加载成功');

                // 延迟调用初始化，确保脚本完全执行
                setTimeout(() => {
                    this.callPriceTemplateInitialization();
                }, 100);
            };

            script.onerror = (error) => {
                console.error('AdminFramework: PriceTemplate.js加载失败', error);
                // 移除加载标记
                this.loadedScripts.delete('PriceTemplate.js');

                // 尝试备用初始化方法
                setTimeout(() => {
                    this.fallbackPriceTemplateInitialization();
                }, 200);
            };

            // 添加到head中
            document.head.appendChild(script);
        },

        /**
         * 调用PriceTemplate初始化函数
         */
        callPriceTemplateInitialization: function() {
            // 检查初始化函数是否可用
            if (typeof window.initializePriceTemplateForFramework === 'function') {
                console.log('AdminFramework: 调用PriceTemplate框架初始化函数');
                window.initializePriceTemplateForFramework();
            } else {
                console.warn('AdminFramework: PriceTemplate初始化函数未找到，尝试等待');

                // 等待函数可用
                let attempts = 0;
                const maxAttempts = 20; // 最多等待2秒

                const waitForFunction = () => {
                    attempts++;
                    if (typeof window.initializePriceTemplateForFramework === 'function') {
                        console.log('AdminFramework: 等待后找到PriceTemplate初始化函数');
                        window.initializePriceTemplateForFramework();
                    } else if (attempts < maxAttempts) {
                        setTimeout(waitForFunction, 100);
                    } else {
                        console.error('AdminFramework: 等待超时，尝试备用初始化');
                        this.fallbackPriceTemplateInitialization();
                    }
                };

                waitForFunction();
            }
        },

        /**
         * PriceTemplate页面备用初始化方法
         */
        fallbackPriceTemplateInitialization: function() {
            console.warn('AdminFramework: 使用PriceTemplate备用初始化方法');

            // 检查关键DOM元素
            const container = document.getElementById('pricing-template-container');
            if (!container) {
                console.error('AdminFramework: PriceTemplate页面容器未找到');
                return;
            }

            // 尝试调用可能存在的其他初始化函数
            if (typeof window.initializePage === 'function') {
                console.log('AdminFramework: 调用通用initializePage函数');
                window.initializePage();
            } else {
                console.error('AdminFramework: 无法找到任何PriceTemplate初始化函数');
            }
        },

        /**
         * 清理当前页面
         */
        cleanupCurrentPage: function() {
            if (!this.currentRoute) return;

            console.log(`清理页面: ${this.currentRoute}`);

            // 清理沙箱环境
            ScriptSandboxManager.cleanup(this.currentRoute);

            // 执行页面特定的清理函数
            const cleanupFn = this.cleanupFunctions.get(this.currentRoute);
            if (cleanupFn && typeof cleanupFn === 'function') {
                try {
                    cleanupFn();
                } catch (error) {
                    console.error(`页面清理失败: ${this.currentRoute}`, error);
                }
            }

            // 清理事件监听器
            this.cleanupEventListeners();
        },

        /**
         * 清理事件监听器
         */
        cleanupEventListeners: function() {
            // 移除可能的全局事件监听器
            // 这里可以根据需要添加具体的清理逻辑
        },

        /**
         * 更新导航状态
         */
        updateNavigationState: function(route) {
            // 移除所有活动状态
            document.querySelectorAll('.af-nav-item').forEach(item => {
                item.classList.remove('af-active');
            });

            // 添加当前路由的活动状态
            const currentNavItem = document.querySelector(`[data-af-route="${route}"]`);
            if (currentNavItem) {
                currentNavItem.classList.add('af-active');
            }
        },

        /**
         * 显示进度条
         */
        showProgressBar: function() {
            const progressBar = document.getElementById('afProgressBar');
            if (progressBar) {
                progressBar.style.width = '0%';
                progressBar.style.opacity = '1';

                // 模拟进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    progressBar.style.width = progress + '%';

                    if (progress >= 90) {
                        clearInterval(interval);
                    }
                }, 100);

                // 存储interval以便后续清理
                this._progressInterval = interval;
            }
        },

        /**
         * 隐藏进度条
         */
        hideProgressBar: function() {
            const progressBar = document.getElementById('afProgressBar');
            if (progressBar) {
                progressBar.style.width = '100%';
                setTimeout(() => {
                    progressBar.style.opacity = '0';
                    setTimeout(() => {
                        progressBar.style.width = '0%';
                    }, 300);
                }, 100);
            }

            // 清理进度条interval
            if (this._progressInterval) {
                clearInterval(this._progressInterval);
                this._progressInterval = null;
            }
        },

        /**
         * 显示错误页面
         */
        showErrorPage: function(route, error) {
            const pageContent = document.getElementById('afPageContent');
            if (pageContent) {
                pageContent.innerHTML = `
                    <div class="af-error-page" style="padding: 40px; text-align: center; color: #666;">
                        <h2 style="color: #e74c3c;">页面加载失败</h2>
                        <p>路由: <code>${route}</code></p>
                        <p>错误信息: ${error.message}</p>
                        <button onclick="AdminFramework.navigateTo('home')"
                                style="padding: 10px 20px; background: #ff6b9d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            返回控制台
                        </button>
                        <button onclick="AdminFramework.navigateTo('${route}')"
                                style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                            重试
                        </button>
                    </div>
                `;
            }
        },

        /**
         * 设置全局错误处理
         */
        setupErrorHandling: function() {
            window.addEventListener('error', (event) => {
                console.error('全局错误:', event.error);
            });

            window.addEventListener('unhandledrejection', (event) => {
                console.error('未处理的Promise拒绝:', event.reason);
            });
        },

        /**
         * 注册页面清理函数
         */
        registerCleanupFunction: function(route, cleanupFn) {
            this.cleanupFunctions.set(route, cleanupFn);
        },

        /**
         * 获取当前路由
         */
        getCurrentRoute: function() {
            return this.currentRoute;
        },

        /**
         * 刷新当前页面
         */
        refresh: function() {
            if (this.currentRoute) {
                this.loadPage(this.currentRoute);
            }
        }
    };

    // 暴露到全局
    window.AdminFramework = AdminFramework;
    window.ScriptSandboxManager = ScriptSandboxManager;

    // 页面加载完成后初始化框架
    document.addEventListener('DOMContentLoaded', function() {
        AdminFramework.init();
    });

})(window);