{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无名SUP - 后台管理框架</title>
    
    <!-- 框架专用样式文件 -->
    <link rel="stylesheet" href="{% static 'css/admin-framework.css' %}">

    <!-- 移动端响应式样式文件 -->
    <link rel="stylesheet" href="{% static 'css/mobile-responsive.css' %}">

    <!-- 预加载资源 -->
    <link rel="preload" href="{% static 'css/admin-framework.css' %}" as="style">
    <link rel="preload" href="{% static 'css/mobile-responsive.css' %}" as="style">
    <link rel="preload" href="{% static 'js/admin-framework.js' %}" as="script">
    
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- 框架内联样式 -->
    <style>
        /* 确保页面在CSS加载前有基本样式 */
        body { margin: 0; font-family: system-ui, -apple-system, sans-serif; }
        .af-loading-overlay {
            position: fixed; top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(255,255,255,0.9); display: flex;
            justify-content: center; align-items: center; z-index: 9999;
        }
    </style>
</head>

<body class="af-body">
    <!-- 页面加载遮罩 -->
    <div id="afLoadingOverlay" class="af-loading-overlay">
        <div class="af-loading-spinner"></div>
    </div>
    
    <!-- 进度条 -->
    <div id="afProgressBar" class="af-progress-bar"></div>
    
    <!-- 顶部信息栏 -->
    <header class="af-header">
        <div class="af-header-left">
            <!-- 菜单切换按钮 - 延用原版样式 -->
            <button id="afMenuToggle" class="af-menu-toggle" aria-label="切换导航菜单">
                <span></span>
                <span></span>
                <span></span>
            </button>
            
            <!-- Logo区域 -->
            <div class="af-logo">
                <i class="fas fa-store af-logo-icon"></i>
                <span class="af-logo-text">无名SUP</span>
            </div>
        </div>
        
        <div class="af-header-right">
            <!-- 顶部信息栏 -->
        </div>
    </header>
    
    <!-- 移动端遮罩层 -->
    <div id="afSidebarOverlay" class="af-sidebar-overlay"></div>

    <!-- 侧边导航栏 -->
    <nav id="afSidebar" class="af-sidebar" aria-label="主导航">
        <!-- 导航内容 -->
        <div class="af-sidebar-content">
            <ul class="af-nav-list">
                <!-- 控制台 - 对应原版 pages/home.html -->
                <li class="af-nav-item af-active" data-af-route="home">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-home af-nav-icon"></i>
                        <span class="af-nav-text">控制台</span>
                    </a>
                </li>

                <!-- 商品列表 - 对应原版 productlist.html -->
                <li class="af-nav-item" data-af-route="productlist">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-boxes af-nav-icon"></i>
                        <span class="af-nav-text">商品列表</span>
                    </a>
                </li>

                <!-- 分类管理 - 对应原版 category.html -->
                <li class="af-nav-item" data-af-route="category">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-folder af-nav-icon"></i>
                        <span class="af-nav-text">分类管理</span>
                    </a>
                </li>

                <!-- 卡密管理 - 对应原版 cardstock.html -->
                <li class="af-nav-item" data-af-route="cardstock">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-key af-nav-icon"></i>
                        <span class="af-nav-text">卡密管理</span>
                    </a>
                </li>

                <!-- 卡券管理 - 对应原版 coupon.html -->
                <li class="af-nav-item" data-af-route="coupon">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-ticket-alt af-nav-icon"></i>
                        <span class="af-nav-text">卡券管理</span>
                    </a>
                </li>

                <!-- 定价模板 - 对应原版 PriceTemplate.html -->
                <li class="af-nav-item" data-af-route="PriceTemplate">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-tags af-nav-icon"></i>
                        <span class="af-nav-text">定价模板</span>
                    </a>
                </li>

                <!-- 对接中心 - 对应原版 DockingCenter.html -->
                <li class="af-nav-item" data-af-route="DockingCenter">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-handshake af-nav-icon"></i>
                        <span class="af-nav-text">对接中心</span>
                    </a>
                </li>

                <!-- 订单列表 - 对应原版 orderlist.html -->
                <li class="af-nav-item" data-af-route="orderlist">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-clipboard-list af-nav-icon"></i>
                        <span class="af-nav-text">订单列表</span>
                    </a>
                </li>

                <!-- 用户列表 - 对应原版 userlist.html -->
                <li class="af-nav-item" data-af-route="userlist">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-user af-nav-icon"></i>
                        <span class="af-nav-text">用户列表</span>
                    </a>
                </li>

                <!-- 会员等级 - 对应原版 usergroup.html -->
                <li class="af-nav-item" data-af-route="usergroup">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-user-tag af-nav-icon"></i>
                        <span class="af-nav-text">会员等级</span>
                    </a>
                </li>

                <!-- 支付设置 - 对应原版 payment.html -->
                <li class="af-nav-item" data-af-route="payment">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-credit-card af-nav-icon"></i>
                        <span class="af-nav-text">支付设置</span>
                    </a>
                </li>

                <!-- 插件市场 - 对应原版 plugin.html -->
                <li class="af-nav-item" data-af-route="plugin">
                    <a href="#" class="af-nav-link">
                        <i class="fas fa-puzzle-piece af-nav-icon"></i>
                        <span class="af-nav-text">插件市场</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- 主内容区域 -->
    <main id="afMainContent" class="af-main-content">
        <div id="afPageContent" class="af-page-content">
            <!-- 这里将通过AJAX加载具体的页面内容 -->
            <div class="af-welcome-message">
                <h1>欢迎使用后台管理系统</h1>
                <p>请从左侧导航菜单选择功能模块</p>
            </div>
        </div>
    </main>

    <!-- 管理员认证工具 -->
    <script src="{% static 'js/admin-auth.js' %}"></script>
    <script src="{% static 'js/admin-framework.js' %}"></script>

    <!-- 框架配置和初始化脚本 -->
    <script>
        // 全局框架配置
        window.AdminFrameworkConfig = {
            // API基础路径
            apiBasePath: '/admin/backend',
            // 框架页面路径
            frameworkPagePath: '/admin/backend/framework/page',
            // 当前路由
            currentRoute: 'home',
            // 调试模式
            debug: true
        };

        // 页面加载完成后隐藏加载动画
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AdminFramework 配置:', window.AdminFrameworkConfig);

            setTimeout(function() {
                const overlay = document.getElementById('afLoadingOverlay');
                if (overlay) {
                    overlay.classList.add('af-hidden');
                }
            }, 500);
        });
    </script>
</body>
</html>
