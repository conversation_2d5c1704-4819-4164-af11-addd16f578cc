<template>
  <div class="caihong-mall">
    <!-- 头部区域 -->
    <header class="header">
      <div class="header-content">
        <!-- 用户头像 -->
        <div class="user-avatar">
          <img :src="userAvatar" alt="用户头像" class="avatar-img">
        </div>
        
        <!-- 网站标题 -->
        <h1 class="site-title">依思商城</h1>
        
        <!-- 导航按钮 -->
        <div class="nav-buttons">
          <button class="nav-btn" @click="showAnnouncement">公告</button>
          <button class="nav-btn" @click="contactService">客服</button>
          <button class="nav-btn" @click="handleLogin">登录</button>
        </div>
      </div>
    </header>

    <!-- 主要功能区域 -->
    <main class="main-content">
      <!-- 搜索栏 -->
      <div class="search-section">
        <div class="search-container">
          <!-- 分类下拉菜单 -->
          <div class="category-dropdown">
            <select v-model="selectedCategory" class="category-select">
              <option value="">选择分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                [[ category.name ]]
              </option>
            </select>
          </div>
          
          <!-- 搜索输入框 -->
          <div class="search-input-container">
            <input 
              type="text" 
              v-model="searchKeyword" 
              placeholder="搜索商品" 
              class="search-input"
              @keyup.enter="handleSearch"
            >
            <button class="search-btn" @click="handleSearch">搜索</button>
          </div>
        </div>
        
        <!-- 分站和更多链接 -->
        <div class="additional-links">
          <a href="#" class="link-btn">分站</a>
          <a href="#" class="link-btn">更多</a>
        </div>
      </div>

      <!-- 公告信息 -->
      <div class="announcement-section">
        <div class="announcement-content">
          <span class="announcement-date">[[ currentDate ]]</span>
          <span class="announcement-text">最新业务通知</span>
          <span class="announcement-tag">热门公告</span>
        </div>
      </div>

      <!-- 立即购买按钮 -->
      <div class="purchase-section">
        <button class="purchase-btn" @click="handlePurchase">立即购买</button>
      </div>

      <!-- 数据统计区域 -->
      <div class="statistics-section">
        <h2 class="statistics-title">数据统计</h2>
        <div class="statistics-grid">
          <div class="stat-item">
            <div class="stat-icon security-icon">🛡️</div>
            <div class="stat-label">安全监控</div>
            <div class="stat-value">[[ statistics.security ]]</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon transaction-icon">🛒</div>
            <div class="stat-label">交易总数</div>
            <div class="stat-value">[[ statistics.totalTransactions ]]</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon order-icon">✅</div>
            <div class="stat-label">订单总数</div>
            <div class="stat-value">[[ statistics.totalOrders ]]</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon agent-icon">⬆️</div>
            <div class="stat-label">代理分站</div>
            <div class="stat-value">[[ statistics.agentSites ]]</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon today-transaction-icon">🛒</div>
            <div class="stat-label">今日交易</div>
            <div class="stat-value">[[ statistics.todayTransactions ]]</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon today-order-icon">✅</div>
            <div class="stat-label">今日订单</div>
            <div class="stat-value">[[ statistics.todayOrders ]]</div>
          </div>
        </div>
      </div>
    </main>

    <!-- 页脚信息 -->
    <footer class="footer">
      <div class="footer-content">
        <div class="copyright">
          依思商城 © [[ currentYear ]]
        </div>
        <div class="ip-address">
          [[ clientIP ]]
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'CaihongMallIndex',
  delimiters: ['[[', ']]'],
  
  data() {
    return {
      // 用户相关
      userAvatar: '/static/images/default-avatar.png',
      
      // 搜索相关
      selectedCategory: '',
      searchKeyword: '',
      categories: [],
      
      // 统计数据
      statistics: {
        security: 0,
        totalTransactions: 0,
        totalOrders: 0,
        agentSites: 0,
        todayTransactions: 0,
        todayOrders: 0
      },
      
      // 时间相关
      currentDate: '',
      currentYear: new Date().getFullYear(),
      
      // IP地址
      clientIP: '获取中...'
    }
  },
  
  async mounted() {
    await this.initializeData()
  },
  
  methods: {
    // 初始化数据
    async initializeData() {
      this.updateCurrentDate()
      await this.loadCategories()
      await this.loadStatistics()
      await this.getClientIP()
    },
    
    // 更新当前日期
    updateCurrentDate() {
      const now = new Date()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      this.currentDate = `[${month}月${day}日]`
    },
    
    // 加载分类数据
    async loadCategories() {
      try {
        const response = await this.$api('/api/categories/')
        if (response.success) {
          this.categories = response.data
        }
      } catch (error) {
        console.error('加载分类失败:', error)
      }
    },
    
    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await this.$api('/api/statistics/')
        if (response.success) {
          this.statistics = response.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
        // 设置默认值
        this.statistics = {
          security: 100,
          totalTransactions: 1234,
          totalOrders: 567,
          agentSites: 12,
          todayTransactions: 89,
          todayOrders: 45
        }
      }
    },
    
    // 获取客户端IP
    async getClientIP() {
      try {
        const response = await this.$api('/api/get-client-ip/')
        if (response.success) {
          this.clientIP = response.ip
        }
      } catch (error) {
        console.error('获取IP失败:', error)
        this.clientIP = '无法获取'
      }
    },
    
    // 事件处理方法
    showAnnouncement() {
      alert('公告功能开发中...')
    },
    
    contactService() {
      alert('客服功能开发中...')
    },
    
    handleLogin() {
      window.location.href = '/user/login/'
    },
    
    handleSearch() {
      if (!this.searchKeyword.trim()) {
        alert('请输入搜索关键词')
        return
      }
      
      const params = new URLSearchParams()
      if (this.selectedCategory) {
        params.append('category', this.selectedCategory)
      }
      params.append('keyword', this.searchKeyword)
      
      window.location.href = `/products/?${params.toString()}`
    },
    
    handlePurchase() {
      if (!this.selectedCategory) {
        alert('请先选择商品分类')
        return
      }
      window.location.href = `/products/?category=${this.selectedCategory}`
    }
  }
}
</script>

<style scoped>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.caihong-mall {
  min-height: 100vh;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  color: #333;
}

/* 头部样式 */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  padding: 15px 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #74b9ff;
  box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.site-title {
  font-size: 28px;
  font-weight: bold;
  color: #2d3436;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  flex: 1;
  text-align: center;
}

.nav-buttons {
  display: flex;
  gap: 10px;
}

.nav-btn {
  padding: 8px 16px;
  background: #74b9ff;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

.nav-btn:hover {
  background: #0984e3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4);
}

/* 主内容区域 */
.main-content {
  max-width: 800px;
  margin: 30px auto;
  padding: 0 20px;
}

/* 搜索区域 */
.search-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.search-container {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.category-dropdown {
  flex: 0 0 200px;
}

.category-select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #ddd;
  border-radius: 25px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-select:focus {
  outline: none;
  border-color: #74b9ff;
  box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.2);
}

.search-input-container {
  flex: 1;
  display: flex;
  gap: 10px;
}

.search-input {
  flex: 1;
  padding: 12px 20px;
  border: 2px solid #ddd;
  border-radius: 25px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #74b9ff;
  box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.2);
}

.search-btn {
  padding: 12px 25px;
  background: #74b9ff;
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
}

.search-btn:hover {
  background: #0984e3;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(116, 185, 255, 0.4);
}

.additional-links {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.link-btn {
  color: #74b9ff;
  text-decoration: none;
  font-weight: bold;
  padding: 5px 15px;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.link-btn:hover {
  background: rgba(116, 185, 255, 0.1);
  color: #0984e3;
}

/* 公告区域 */
.announcement-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 15px 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.announcement-content {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.announcement-date {
  color: #74b9ff;
  font-weight: bold;
}

.announcement-text {
  flex: 1;
  color: #2d3436;
}

.announcement-tag {
  background: #ff7675;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
}

/* 购买按钮区域 */
.purchase-section {
  text-align: center;
  margin-bottom: 30px;
}

.purchase-btn {
  padding: 15px 60px;
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: white;
  border: none;
  border-radius: 30px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(0, 184, 148, 0.3);
}

.purchase-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(0, 184, 148, 0.4);
}

/* 统计区域 */
.statistics-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.statistics-title {
  font-size: 20px;
  font-weight: bold;
  color: #2d3436;
  margin-bottom: 20px;
  text-align: center;
  position: relative;
}

.statistics-title::before {
  content: '📊';
  margin-right: 8px;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px 15px;
  background: rgba(116, 185, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.stat-item:hover {
  transform: translateY(-5px);
  background: rgba(116, 185, 255, 0.1);
  border-color: rgba(116, 185, 255, 0.3);
  box-shadow: 0 8px 24px rgba(116, 185, 255, 0.2);
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #636e72;
  margin-bottom: 5px;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #2d3436;
}

/* 页脚样式 */
.footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20px 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  font-size: 14px;
  color: #636e72;
}

.copyright {
  font-weight: 500;
}

.ip-address {
  font-family: 'Courier New', monospace;
  background: rgba(116, 185, 255, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .site-title {
    font-size: 24px;
  }

  .search-container {
    flex-direction: column;
  }

  .category-dropdown {
    flex: none;
  }

  .search-input-container {
    flex-direction: column;
  }

  .statistics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .footer-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .main-content {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .statistics-grid {
    grid-template-columns: 1fr;
  }

  .nav-buttons {
    flex-wrap: wrap;
    justify-content: center;
  }

  .purchase-btn {
    padding: 12px 40px;
    font-size: 16px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-section,
.announcement-section,
.purchase-section,
.statistics-section {
  animation: fadeInUp 0.6s ease-out;
}

.search-section {
  animation-delay: 0.1s;
}

.announcement-section {
  animation-delay: 0.2s;
}

.purchase-section {
  animation-delay: 0.3s;
}

.statistics-section {
  animation-delay: 0.4s;
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(116, 185, 255, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(116, 185, 255, 0.7);
}
</style>
